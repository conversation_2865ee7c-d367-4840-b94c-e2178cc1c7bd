package com.pacto.plano.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.Uteis;
import com.pacto.plano.config.swagger.respostas.plano.redeempresa.EnvelopeRespostaListPlanoRedeEmpresa;
import com.pacto.plano.config.swagger.respostas.plano.redeempresa.EnvelopeRespostaPlanoRedeEmpresa;
import com.pacto.plano.services.interfaces.PlanoRedeEmpresaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.pacto.plano.config.swagger.SwaggerTags.PLANO;
import static com.pacto.plano.config.swagger.respostas.plano.redeempresa.EnvelopeRespostaListPlanoRedeEmpresa.PLANO_REDE_EMPRESA_RESPOSTA_LIST;
import static com.pacto.plano.config.swagger.respostas.plano.redeempresa.EnvelopeRespostaPlanoRedeEmpresa.PLANO_REDE_EMPRESA_RESPOSTA;

@RestController
@RequestMapping("/planoRedeEmpresa")
@Tag(name = PLANO)
public class PlanoRedeEmpresaController {

    private final PlanoRedeEmpresaService planoRedeEmpresaService;

    @Autowired
    public PlanoRedeEmpresaController(PlanoRedeEmpresaService planoRedeEmpresaService) {
        this.planoRedeEmpresaService = planoRedeEmpresaService;
    }


    @Operation(
            summary = "Replicar plano entre rede de empresas",
            description = "Replica um plano entre as redes de empresas",
            parameters = {
                    @Parameter(name = "planoOrigem", description = "Código do plano que será replicado", example = "1", required = true),
                    @Parameter(name = "chaveDestino", description = "Chave da empresa que receberá o plano", example = "rede123-unidade456", required = true),
                    @Parameter(name = "empresaDestino", description = "Código da empresa que receberá o plano", example = "1", required = true),
                    @Parameter(name = "empresaId", description = "Código identificador da empresa que o plano será replicado", required = true, example = "1", in = ParameterIn.HEADER)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPlanoRedeEmpresa.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = PLANO_REDE_EMPRESA_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("replicar/{planoOrigem}/{chaveDestino}/{empresaDestino}")
    public ResponseEntity<EnvelopeRespostaDTO> replicar(@PathVariable Integer planoOrigem, @PathVariable String chaveDestino, @PathVariable Integer empresaDestino) {
        try {
            return ResponseEntityFactory.ok(planoRedeEmpresaService.replicar(planoOrigem, chaveDestino, empresaDestino));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Remover vinculo de um plano que foi replicado",
            description = "Remove o vínculo de um plano que foi replicado",
            parameters = {
                    @Parameter(name = "planoOrigem", description = "Código do plano de origem", example = "1", required = true),
                    @Parameter(name = "chaveDestino", description = "Chave da empresa de destino", example = "1", required = true),
                    @Parameter(name = "empresaDestino", description = "Código da empresa de destino", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            }
    )
    @PostMapping("removerVinculoReplicacao/{planoOrigem}/{chaveDestino}/{empresaDestino}")
    public ResponseEntity<EnvelopeRespostaDTO> removerVinculoReplicacao(@PathVariable Integer planoOrigem, @PathVariable String chaveDestino, @PathVariable Integer empresaDestino) {
        try {
            planoRedeEmpresaService.removerVinculoReplicacao(planoOrigem, chaveDestino, empresaDestino);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Replicar plano para todas as empresas da rede da academia",
            description = "Replica o plano para todas as empresas da rede da academia",
            parameters = {
                    @Parameter(name = "planoOrigem", description = "Código do plano de origem que será replicado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            }
    )
    @PostMapping("replicarAutomaticoTodosReplicados/{planoOrigem}")
    public ResponseEntity<EnvelopeRespostaDTO> replicarAutomaticoTodosReplicados(@PathVariable Integer planoOrigem) {
        try {
            planoRedeEmpresaService.replicarAutomaticoTodosReplicados(planoOrigem);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar planos de rede de empresas",
            description = "Consulta um plano de rede de empresas através do código dele",
            parameters = {
                    @Parameter(name = "plano", description = "Código do plano que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPlanoRedeEmpresa.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = PLANO_REDE_EMPRESA_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("plano/{plano}")
    public ResponseEntity<EnvelopeRespostaDTO> findByPlano(@PathVariable Integer plano) {
        try {
            Uteis.logar(null, "Entrou no findPlano");
            return ResponseEntityFactory.ok(planoRedeEmpresaService.findByPlano(plano));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar plano de rede de empresas",
            description = "Consulta um plano de rede de empresas através do código dele",
            parameters = {
                    @Parameter(name = "codigo", description = "Código identificador do plano de rede que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPlanoRedeEmpresa.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = PLANO_REDE_EMPRESA_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer codigo) {
        try {
            return ResponseEntityFactory.ok(planoRedeEmpresaService.findById(codigo));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
